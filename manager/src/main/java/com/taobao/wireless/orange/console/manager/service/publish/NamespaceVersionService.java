package com.taobao.wireless.orange.console.manager.service.publish;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.taobao.wireless.orange.console.manager.*;
import com.taobao.wireless.orange.console.manager.api.impl.CommonApiManager;
import com.taobao.wireless.orange.console.manager.api.model.ErrorCodeConstant;
import com.taobao.wireless.orange.console.manager.api.model.PageInfo;
import com.taobao.wireless.orange.console.manager.api.model.PageResult;
import com.taobao.wireless.orange.console.manager.api.model.Result;
import com.taobao.wireless.orange.console.manager.api.model.publish.VersionQueryCondition;
import com.taobao.wireless.orange.console.manager.impl.NamespaceVersionManagerImpl;
import com.taobao.wireless.orange.console.manager.manager.config.PublishConfigManager;
import com.taobao.wireless.orange.console.manager.manager.config.SysConfigManager;
import com.taobao.wireless.orange.console.manager.manager.model.GrayPushTypeEnum;
import com.taobao.wireless.orange.console.manager.manager.model.RollbackVersionView;
import com.taobao.wireless.orange.console.manager.manager.namespace.NamespaceChangeManager;
import com.taobao.wireless.orange.console.manager.manager.record.RecordManagerV2;
import com.taobao.wireless.orange.console.manager.manager.setting.BusinessUtils;
import com.taobao.wireless.orange.console.manager.manager.utils.NamespaceVersionUtils;
import com.taobao.wireless.orange.console.manager.manager.version.CreateVersionManager;
import com.taobao.wireless.orange.console.manager.manager.version.RollbackVersionManager;
import com.taobao.wireless.orange.console.manager.manager.version.VersionRatioGrayManager;
import com.taobao.wireless.orange.console.manager.model.NamespaceGrayRecordBO;
import com.taobao.wireless.orange.console.manager.model.User;
import com.taobao.wireless.orange.console.manager.service.ConsoleClientInfo;
import com.taobao.wireless.orange.console.manager.service.view.*;
import com.taobao.wireless.orange.console.manager.support.MassGraySupport;
import com.taobao.wireless.orange.console.manager.support.MassManager;
import com.taobao.wireless.orange.console.manager.support.data.DpReportManager;
import com.taobao.wireless.orange.console.manager.support.diamond.DiamondCommonDataManager;
import com.taobao.wireless.orange.console.manager.support.tiga.TigaManager;
import com.taobao.wireless.orange.console.manager.support.user.UserManager;
import com.taobao.wireless.orange.console.manager.support.user.UserUtils;
import com.taobao.wireless.orange.console.manager.util.logger.ServiceLoggerUtil;
import com.taobao.wireless.orange.core.common.Constant;
import com.taobao.wireless.orange.core.dao.common.GmtTimeCondition;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.common.OtherCondition;
import com.taobao.wireless.orange.core.dao.common.Pagination;
import com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO;
import com.taobao.wireless.orange.core.exception.BusinessException;
import com.taobao.wireless.orange.core.service.model.NamespaceBO;
import com.taobao.wireless.orange.core.service.model.NamespaceChangeBO;
import com.taobao.wireless.orange.core.service.model.NamespaceVersionBO;
import com.taobao.wireless.orange.core.service.model.ResourceDataBO;
import com.taobao.wireless.orange.core.type.Source;
import com.taobao.wireless.orange.core.type.Status;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;

import static com.taobao.wireless.orange.console.manager.api.model.NamespaceVersionQueryCondition.DATE_FORMATTER;
import static com.taobao.wireless.orange.console.manager.expression.Fields.DID_HASH;

public class NamespaceVersionService {
    private static final Logger logger = LoggerFactory.getLogger(NamespaceVersionService.class);

    private static final Set<String> EMAS_SUPPORT_DIMENSIONS = ImmutableSet.of("os_ver", "m_brand", "m_model", "app_ver");

    @Autowired
    private NamespaceManager namespaceManager;

    @Autowired
    private SysConfigManager sysConfigManager;

    @Autowired
    private MassManager massManager;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private ResourceDataManager resourceDataManager;

    @Autowired
    private DiamondCommonDataManager diamondCommonDataManager;
    @Autowired
    @Qualifier("indexManager")
    private IndexManager indexManager;
    @Autowired
    private CommonApiManager commonApiManager;
    @Autowired
    private RecordManagerV2 recordManagerV2;
    @Autowired
    private AppManager appManager;
    @Autowired
    private ProbeTaskManager probeTaskManager;
    @Autowired
    private UserManager userManager;

    @Autowired
    private CreateVersionManager createVersionManager;

    @Autowired
    private RollbackVersionManager rollbackVersionManager;
    @Autowired
    private NamespaceChangeManager namespaceChangeManager;
    @Autowired
    private DpReportManager dpReportManager;
    @Autowired
    private MassGraySupport massGraySupport;
    @Autowired
    private PublishConfigManager publishConfigManager;
    @Autowired
    private PublishControlManager publishControlManager;
    @Autowired
    private VersionRatioGrayManager versionRatioGrayManager;
    @Autowired
    private TigaManager tigaManager;

    public Result<PageResult<NamespaceVersionBO>> queryList(VersionQueryCondition condition, PageInfo pageInfo, ConsoleClientInfo clientInfo) {
        String requestParameters = condition != null ? "appKey= " + condition.getAppKey() + "; ns=" + condition.getName() : "";
        Result<PageResult<NamespaceVersionBO>> result = new Result<>();
        ServiceLoggerUtil.logServiceInput("NamespaceVersionService", "queryList", clientInfo, requestParameters);
        try {

            if (condition == null) {
                throw new BusinessException("Condition for query is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }

            NamespaceVersionBO nvb = new NamespaceVersionBO();
            nvb.setAppKey(condition.getAppKey());
            nvb.setAppVersion(condition.getAppVersion());
            nvb.setVersion(condition.getNamespaceVersion());
            nvb.setNamespaceId(condition.getNamespaceId());
            nvb.setName(condition.getName());
            nvb.setCreator(condition.getCreator());
            if (condition.getSource() != null) {
                Source source = Source.convert(condition.getSource());
                if (source != null) {
                    nvb.setSource(source.getCode());
                }
            }
            if (condition.getOnline() != null) {
                nvb.setIsAvailable(condition.getOnline() ? Constant.Y : Constant.N);
            }
            OtherCondition otherCondition = new OtherCondition();
            if (condition.getStatus() != null) {
                Status status = Status.convert(condition.getStatus());
                if (status != null) {
                    otherCondition.setStatus(status);
                }
            }
            GmtTimeCondition gmtTimeCondition = new GmtTimeCondition();
            if (StringUtils.isNotBlank(condition.getPublishFrom())) {
                try {
                    new SimpleDateFormat(DATE_FORMATTER).parse(condition.getPublishFrom());
                } catch (ParseException e) {
                    throw new BusinessException("Condition publishFrom date format error.",
                            ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
                }
                gmtTimeCondition.setGmtPublishS(condition.getPublishFrom());
            }
            if (StringUtils.isNotBlank(condition.getPublishTo())) {
                try {
                    new SimpleDateFormat(DATE_FORMATTER).parse(condition.getPublishTo());
                } catch (ParseException e) {
                    throw new BusinessException("Condition publishTo date format error.",
                            ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
                }
                gmtTimeCondition.setGmtPublishE(condition.getPublishTo());
            }
            if (StringUtils.isNotBlank(condition.getCreateFrom())) {
                try {
                    new SimpleDateFormat(DATE_FORMATTER).parse(condition.getCreateFrom());
                } catch (ParseException e) {
                    throw new BusinessException("Condition createFrom date format error.",
                            ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
                }
                gmtTimeCondition.setGmtCreateS(condition.getCreateFrom());
            }
            if (StringUtils.isNotBlank(condition.getCreateTo())) {
                try {
                    new SimpleDateFormat(DATE_FORMATTER).parse(condition.getCreateTo());
                } catch (ParseException e) {
                    throw new BusinessException("Condition createTo date format error.",
                            ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
                }
                gmtTimeCondition.setGmtCreateE(condition.getCreateTo());
            }
            Pagination pagination = commonApiManager.checkAndSetPageInfo(pageInfo);
            List<NamespaceVersionBO> list = namespaceVersionManager.selectForPagination(nvb, otherCondition,
                    gmtTimeCondition, OrderBy.ORDER_BY_ID_DESC, pagination);
            PageResult<NamespaceVersionBO> pageResult = new PageResult<>();
            pageResult.setContent(list);
            pageResult.setTotal(pagination.getItemCount());
            pageResult.setCurPage(pagination.getPageNo());
            pageResult.setPageSize(pagination.getPageSize());
            result.setModel(pageResult);
            result.setIsSuccess(true);
            ServiceLoggerUtil.logServiceResult("NamespaceVersionService", "queryList", clientInfo, requestParameters, pagination.getItemCount());
        } catch (BusinessException e) {
            result.setIsSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "queryList", clientInfo, requestParameters, e);
        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setErrorCode(ErrorCodeConstant.SYSTEM_ERROR);
            result.setErrorMsg("system error, please try it again!" + e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "queryList", clientInfo, requestParameters, e);

        }
        return result;
    }

    /***
     * 查询namespace发布详情
     * @param namespaceId namespaceId
     * @param version 发布版本
     * @param needResource 是否需要resource content,
     * @param clientInfo  api调用业务方名称 与授权
     * @return 版本详情
     */
    public Result<VersionDetailView> queryDetail(String namespaceId, String version, boolean needResource, ConsoleClientInfo clientInfo) {
        String requestParameters = "ns = " + namespaceId + " ,version=" + version;
        ServiceLoggerUtil.logServiceInput("NamespaceVersionService", "queryDetail", clientInfo, requestParameters);
        Result<VersionDetailView> result = new Result<>();
        try {
            if (StringUtils.isBlank(namespaceId)) {
                throw new BusinessException("namespaceId is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            if (StringUtils.isBlank(version)) {
                throw new BusinessException("version is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            NamespaceVersionBO versionBO = namespaceVersionManager.selectByNamespaceIdAndVersion(namespaceId, version);
            if (versionBO != null) {
                VersionDetailView versionDetailView = new VersionDetailView();
                versionDetailView.setVersionBO(versionBO);
                ViewConfig viewConfig = new ViewConfig();
                NamespaceBO namespaceBO = namespaceManager.selectAvailableByNamespaceId(namespaceId);
                if (namespaceBO != null) {
                    versionDetailView.setNamespaceBO(namespaceBO);
                } else {
                    throw new BusinessException("namespace not exist", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
                }
                versionDetailView.setAppBO(appManager.selectAppByAppKey(namespaceBO.getAppKeyOrGroup()));
                versionDetailView.setRecordList(recordManagerV2.selectByNamespaceIdAndVersion(namespaceId, version));
                if (needResource) {
                    ResourceDataBO resourceDataBO = resourceDataManager.selectByResourceId(versionBO.getResourceId());
                    if (resourceDataBO != null) {
                        versionDetailView.setResourceBO(resourceDataBO);
                    }
                }
                WmccItemView itemView = probeTaskManager.getWmccItemView(versionBO);
                versionDetailView.setWmccView(itemView);
                versionDetailView.setIndexBO(indexManager.selectAvailableByAppVersion(versionBO.getAppKey(), versionBO.getAppVersion()));

                //上一个版本
                NamespaceVersionBO lastVersionBO = null;
                if (StringUtils.isNotEmpty(versionBO.getPreviousResourceId())) {
                    lastVersionBO = namespaceVersionManager.selectByResourceId(versionBO.getPreviousResourceId());
                    versionDetailView.setLastVersionBO(lastVersionBO);
                }
                //新版提供rollback 和 关联版本信息
                if (StringUtils.isNotEmpty(versionBO.getOfflines())) {
                    JSONObject offlines = JSONObject.parseObject(versionBO.getOfflines());
                    if (offlines.size() > 0) {
                        List<NamespaceVersionBO> offlineList = namespaceVersionManager.selectByNamespaceIdAndVersion(namespaceId, new ArrayList<>(offlines.keySet()));
                        versionDetailView.setOfflineList(offlineList);
                    }
                }
                if (StringUtils.equalsIgnoreCase(versionBO.getAppVersion(), "*")) {
                    //关联版本
                    NamespaceChangeBO changeBO = namespaceChangeManager.queryOneForNamespace(namespaceId, version, null);
                    versionDetailView.setChangeBO(changeBO);

                    // 填充百分比灰度相关信息
                    NamespaceGrayRecordDO latestGrayRecord = this.versionRatioGrayManager.getGrayRecordByChange(changeBO);
                    if (latestGrayRecord != null) {
                        NamespaceGrayRecordBO grayRecord = new NamespaceGrayRecordBO();
                        BeanUtils.copyProperties(latestGrayRecord, grayRecord);
                        versionDetailView.setLatestGrayRecord(grayRecord);
                    }

                    if (StringUtils.isNotEmpty(versionBO.getVersions())) {
                        String versions = StringUtils.replace(versionBO.getVersions(), "-", version);
                        List<String> orders = Arrays.asList(StringUtils.split(versions, ","));
                        List<NamespaceVersionBO> onlineList = namespaceVersionManager.selectByNamespaceIdAndVersion(namespaceId, orders);
                        versionDetailView.setOnlineList(NamespaceVersionUtils.sortByOrder(onlineList, orders));
                    }

                    if (versionBO.getStatus() == Status.OK.getCode() || versionBO.getStatus() == Status.DELETE.getCode()) {
                        //已发布的或确认是否可回滚
                        if (diamondCommonDataManager.openRollback()) {
                            if (changeBO != null && changeBO.isValid()) {
                                //第一个有效变更可回滚
                                if (lastVersionBO != null && (StringUtils.isNotEmpty(lastVersionBO.getVersions()))) {
                                    viewConfig.setRollback(true);
                                }
                            }
                        }
                    } else {
                        //2020.6月份下线，过程中保留常规版本
                        List<NamespaceVersionBO> noStrategyList = namespaceVersionManager.selectWithoutStrategyAvailableList(namespaceId, OrderBy.ORDER_BY_ID_DESC);
                        versionDetailView.setNoStrategyList(noStrategyList);
                    }
                }
                viewConfig.setWholeProcess(publishControlManager.checkWholeProcess(versionBO.getAppKey()));
                viewConfig.setOneStepSkip(diamondCommonDataManager.isConsoleOneStepSkip(clientInfo.getUser().getEmpId()));
                viewConfig.setShowReport(dpReportManager.showReport(versionBO.getAppKey(), clientInfo.getUser()));
                viewConfig.setRollbackAny(publishConfigManager.openRollbackAnyVersion(namespaceBO.getAppKeyOrGroup()));
                viewConfig.setRatioGray(this.versionRatioGrayManager.isAllowDoRatioGray(versionBO.getAppKey()));
                viewConfig.setShowReport(this.tigaManager.isSupportSmallGray(versionBO));
                versionDetailView.setViewConfig(viewConfig);
                Status status = Status.convert(versionBO.getStatus());
                if (status.isRunning()) {
                    versionDetailView.setGrayConfig(getGrayConfig(versionBO));
                    // 填充百分比灰度是否支持，如果不支持返回不支持原因
                    versionDetailView.setRatioGrayConfig(getRatioGrayConfig(versionBO, lastVersionBO));
                }
                //填充用户相关内容
                Set<String> empIdSet = UserUtils.findUserEmpIds(namespaceBO);
                empIdSet.addAll(UserUtils.findUserEmpIds(versionBO));
                if (CollectionUtils.isNotEmpty(versionDetailView.getRecordList())) {
                    versionDetailView.getRecordList().forEach(item -> {
                        empIdSet.add(item.getCreator());
                    });
                }
                versionDetailView.setUserMap(userManager.queryUserByEmpIds(new ArrayList<>(empIdSet)));
                result.setModel(versionDetailView);
            }
            ServiceLoggerUtil.logServiceResult("NamespaceVersionService", "queryDetail", clientInfo, requestParameters, true);

        } catch (BusinessException e) {
            result.setIsSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "queryDetail", clientInfo, requestParameters, e);

        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setErrorCode(ErrorCodeConstant.SYSTEM_ERROR);
            result.setErrorMsg("系统错误，请稍候重试, 错误信息如下:" + e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "queryDetail", clientInfo, requestParameters, e);
        }
        return result;
    }

    private RatioGrayConfig getRatioGrayConfig(NamespaceVersionBO versionBO, NamespaceVersionBO lastVersionBO) {
        String unsupportedMsg = "";
        if (!this.versionRatioGrayManager.isAllowDoRatioGray(versionBO.getAppKey())) {
            unsupportedMsg = "百分比灰度功能被关闭";
        } else if (versionBO.getSourceDataMeta() != null && BooleanUtils.isTrue(versionBO.getSourceDataMeta().getRollback())) {
            unsupportedMsg = "回滚不支持百分比灰度";
        } else if (!"*".equals(versionBO.getAppVersion())) {
            unsupportedMsg = "仅支持 appVersion 为 * 的版本百分比灰度";
        } else if (StringUtils.isBlank(versionBO.getStrategy()) && lastVersionBO == null) {
            unsupportedMsg = "兜底策略首次发布不支持百分比灰度";
        } else if (StringUtils.isNotBlank(versionBO.getStrategy()) && versionBO.getStrategy().contains(DID_HASH)) {
            unsupportedMsg = "策略带 did_hash 的版本不支持百分比灰度";
        } else if ((StringUtils.isNotBlank(versionBO.getOverwriteStrategyVersions()))) {
            List<NamespaceVersionBO> overwriteNamespaceVersions = this.namespaceVersionManager.selectByNamespaceIdAndVersion(versionBO.getNamespaceId(), Arrays.asList(versionBO.getOverwriteStrategyVersions().split(",")));
            for (NamespaceVersionBO overwriteNamespaceVersion : overwriteNamespaceVersions) {
                if (StringUtils.isNotBlank(overwriteNamespaceVersion.getStrategy()) && overwriteNamespaceVersion.getStrategy().contains(DID_HASH)) {
                    unsupportedMsg = "被删除策略带 did_hash 的版本不支持百分比灰度";
                    break;
                }
            }
        }

        // 兜底展示的错误原因
        if (StringUtils.isBlank(unsupportedMsg) && StringUtils.isBlank(versionBO.getGrayVersions())) {
            unsupportedMsg = "当前版本不支持百分比灰度能力";
        }

        RatioGrayConfig ratioGrayConfig = new RatioGrayConfig();
        ratioGrayConfig.setSupport(StringUtils.isBlank(unsupportedMsg));
        ratioGrayConfig.setUnsupportedMsg(unsupportedMsg);
        return ratioGrayConfig;
    }

    private GrayConfig getGrayConfig(NamespaceVersionBO versionBO) {
        GrayConfig grayConfig = new GrayConfig();

        // 设置策略表达式
        // FIXME: 此处应该使用Orange策略表达式
        boolean parseSuccess = true;
        try {
            grayConfig.setExpression(massGraySupport.toMassExpression4Automatic(versionBO.getAppKey(), versionBO.getName(), versionBO.getVersion(), versionBO.getStrategy()));
        } catch (Exception ex) {
            parseSuccess = false;
            logger.error("Failed to parse expression", ex);
        }

        // 设置灰度配置
        GraySupportConfig massGrayConfig = getMassCircleGrayConfig(versionBO.getAppKey(), versionBO.getName(), versionBO.getVersion(), versionBO.getStrategy());
        ImmutableMap<String, GraySupportConfig> supportConfigs = ImmutableMap.of(
                GrayPushTypeEnum.MASS_CIRCLE.getDetail(), massGrayConfig);
        grayConfig.setSupportConfigs(supportConfigs);


        return grayConfig;
    }

    /**
     * 获取新版圈选灰度配置
     *
     * @param appKey           appKey
     * @param namespaceName    namespace名称
     * @param namepsaceVersion namespace版本
     * @param grayStrategy     灰度策略的表达式
     * @return 灰度配置
     */
    private GraySupportConfig getMassCircleGrayConfig(String appKey, String namespaceName, String namepsaceVersion, String grayStrategy) {
        GraySupportConfig supportConfig = new GraySupportConfig();
        // 查询灰度圈选是否支持
        supportConfig.setSupport(true);

        // 设置新版圈选灰度最大设备数
        supportConfig.setMaxGrayCnt(massGraySupport.getMaxGrayCnt(appKey));

        // 设置支持的灰度维度
        Set<String> circleSelectDimensions = massGraySupport.getCircleSelectDimensions(appKey);
        supportConfig.setSupportDimensions(circleSelectDimensions);

        return supportConfig;
    }

    /***
     * 查询namespace发布详情
     * @param namespaceId namespaceId
     * @param clientInfo  api调用业务方名称 与授权
     * @return 版本详情
     */
    public Result<NamespaceDebugView> queryDebugDetail(String namespaceId, ConsoleClientInfo clientInfo) {
        String requestParameters = "ns = " + namespaceId;
        ServiceLoggerUtil.logServiceInput("NamespaceVersionService", "queryDebugDetail", clientInfo, requestParameters);
        Result<NamespaceDebugView> result = new Result<>();
        final String appVersion = "*";
        try {
            if (StringUtils.isBlank(namespaceId)) {
                throw new BusinessException("namespaceId is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            NamespaceBO namespaceBO = namespaceManager.selectAvailableByNamespaceId(namespaceId);
            if (namespaceBO == null) {
                throw new BusinessException("namespace is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            String appKey = namespaceBO.getAppKeyOrGroup();
            NamespaceDebugView debugView = new NamespaceDebugView();
            NamespaceChangeBO changeBO = namespaceChangeManager.queryOneOKAvailableForNamespace(namespaceId);
            debugView.setChangeBO(changeBO);
            if (changeBO != null) {
                String version = changeBO.getVersionVersion();
                NamespaceVersionBO versionBO = namespaceVersionManager.selectByNamespaceIdAndVersion(namespaceId, version);
                debugView.setVersionBO(versionBO);
                WmccItemView itemView = probeTaskManager.getWmccItemView(versionBO);
                debugView.setWmccView(itemView);
                debugView.setList(namespaceVersionManager.selectByNamespaceIdAndVersionsWithOrder(namespaceId, changeBO.getVersions()));
            }
            debugView.setAppBO(appManager.selectAppByAppKey(namespaceBO.getAppKeyOrGroup()));
            debugView.setNamespaceBO(namespaceBO);
            debugView.setIndexBO(indexManager.selectAvailableByAppVersion(appKey, appVersion));
            //填充用户相关内容
            Set<String> empIdSet = UserUtils.findUserEmpIds(namespaceBO);
            if (CollectionUtils.isNotEmpty(debugView.getList())) {
                debugView.getList().forEach(item -> {
                    empIdSet.addAll(UserUtils.findUserEmpIds(item));
                });
            }
            debugView.setUserMap(userManager.queryUserByEmpIds(new ArrayList<>(empIdSet)));

            result.setModel(debugView);
            ServiceLoggerUtil.logServiceResult("NamespaceVersionService", "queryDebugDetail", clientInfo, requestParameters, true);

        } catch (BusinessException e) {
            result.setIsSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "queryDebugDetail", clientInfo, requestParameters, e);

        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setErrorCode(ErrorCodeConstant.SYSTEM_ERROR);
            result.setErrorMsg("系统错误，请稍候重试！");
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "queryDetail", clientInfo, requestParameters, e);
        }
        return result;
    }

    /***
     * 创建版本
     * @param namespaceId namespaceId
     * @param content 发布content
     * @param clientInfo  api调用业务方名称 与授权
     * @return 版本详情
     */
    public Result<VersionItemView> createVersion(String namespaceId, String content, String scenesContents, String strategy, String overwriteVersions, String orders, ConsoleClientInfo clientInfo) {
        String requestParameters = "ns = " + namespaceId + " ,overwrite=" + overwriteVersions;
        ServiceLoggerUtil.logServiceInput("NamespaceVersionService", "createVersion", clientInfo, requestParameters);
        Result<VersionItemView> result = new Result<>();
        Long begin = null;
        try {
            User user = clientInfo.getUser();
            if (StringUtils.isBlank(namespaceId)) {
                throw new BusinessException("namespaceId is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            NamespaceDetailView detailView = new NamespaceDetailView();
            NamespaceBO namespaceBO = namespaceManager.selectAvailableByNamespaceId(namespaceId);
            if (namespaceBO != null) {
                detailView.setNamespaceBO(namespaceBO);
            } else {
                throw new BusinessException("namespace not exist", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            if (StringUtils.isBlank(content)) {
                throw new BusinessException("content不能为空", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            if (StringUtils.isNotBlank(strategy)) {
                strategy = strategy.trim();
                if (StringUtils.containsIgnoreCase(strategy, "appVersion")) {
                    throw new BusinessException("策略表达式错误，请使用app_ver替换appVersion ！！", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
                }
            }
            checkPermission(namespaceBO, user);
            begin = System.currentTimeMillis();
            createVersionManager.lockNamespace(namespaceId);
            String version = createVersionManager.createVersion(namespaceBO, "*", content, scenesContents, null, strategy, overwriteVersions, false, orders, false, clientInfo);

            VersionItemView itemView = new VersionItemView();
            itemView.setVersion(version);
            itemView.setNamespaceId(namespaceId);
            result.setModel(itemView);
            ServiceLoggerUtil.logServiceResult("NamespaceVersionService", "createVersion", clientInfo, requestParameters, true);

        } catch (BusinessException e) {
            if (StringUtils.equalsIgnoreCase(ErrorCodeConstant.NAMESPACE_LOCK_ERROR, e.getErrorCode())) {
                begin = null;
            }
            result.setIsSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "createVersion", clientInfo, requestParameters, e);

        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setErrorCode(ErrorCodeConstant.SYSTEM_ERROR);
            result.setErrorMsg("系统错误，请稍候重试！");
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "createVersion", clientInfo, requestParameters, e);
        } finally {
            createVersionManager.unlockNamespace(namespaceId, begin);
        }
        return result;
    }

    public Result<List<NamespaceVersionManagerImpl.KnockoutNamespaceVersionBO>> queryKnockoutVersions(String namespaceId, String strategy, String overwriteVersions, ConsoleClientInfo clientInfo) {
        String requestParameters = "ns = " + namespaceId + " ,overwrite=" + overwriteVersions;
        ServiceLoggerUtil.logServiceInput("NamespaceVersionService", "queryKnockoutVersions", clientInfo, requestParameters);
        Result<List<NamespaceVersionManagerImpl.KnockoutNamespaceVersionBO>> result = new Result<>();
        try {
            User user = clientInfo.getUser();
            if (StringUtils.isBlank(namespaceId)) {
                throw new BusinessException("namespaceId is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            NamespaceDetailView detailView = new NamespaceDetailView();
            NamespaceBO namespaceBO = namespaceManager.selectAvailableByNamespaceId(namespaceId);
            if (namespaceBO == null) {
                throw new BusinessException("namespace not exist", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            checkPermission(namespaceBO, user);
            List<String> overwriteStrategyVersions = null;
            if (StringUtils.isNotBlank(overwriteVersions)) {
                overwriteStrategyVersions = Arrays.asList(overwriteVersions.split(","));
            }
            final int maxStrategyCnt = diamondCommonDataManager.getMaxHistoryVersionWithStrategy(BusinessUtils.BUSINESS_SELF, namespaceBO.getName());
            List<NamespaceVersionManagerImpl.KnockoutNamespaceVersionBO> knockoutList = namespaceVersionManager.checkoutKnockoutVersions(namespaceId,
                    "*",
                    namespaceBO.getAppKeyOrGroup(), strategy, overwriteStrategyVersions, maxStrategyCnt, false);
            result.setModel(knockoutList);
            ServiceLoggerUtil.logServiceResult("NamespaceVersionService", "queryKnockoutVersions", clientInfo, requestParameters, true);

        } catch (BusinessException e) {
            result.setIsSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "queryKnockoutVersions", clientInfo, requestParameters, e);

        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setErrorCode(ErrorCodeConstant.SYSTEM_ERROR);
            result.setErrorMsg("系统错误，请稍候重试！");
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "queryKnockoutVersions", clientInfo, requestParameters, e);
        }
        return result;
    }

    /***
     * 查询namespace发布回滚详情（旧版，回滚到上一个版本）
     * @param namespaceId namespaceId
     * @param clientInfo  api调用业务方名称 与授权
     * @return 版本详情
     */
    public Result<RollbackVersionView> getRollbackView(String namespaceId, String fromVersion, String toVersion, ConsoleClientInfo clientInfo) {
        String requestParameters = "nsId=" + namespaceId + ";toVersion=" + toVersion;

        ServiceLoggerUtil.logServiceInput("NamespaceVersionService", "getRollbackView", clientInfo, requestParameters);

        Result<RollbackVersionView> result = new Result<>();
        try {
            if (!diamondCommonDataManager.openRollback()) {
                throw new BusinessException("回滚功能未打开", ErrorCodeConstant.PROHIBIT_OPERATION);
            }
            User user = clientInfo.getUser();
            if (user == null) {
                throw new BusinessException("operator is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            RollbackVersionView view = rollbackVersionManager.getAndCheckRollBackView(namespaceId, fromVersion, toVersion);


            result.setModel(view);
            ServiceLoggerUtil.logServiceResult("NamespaceVersionService", "getRollbackView", clientInfo, requestParameters, true);

        } catch (BusinessException e) {
            result.setIsSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "getRollbackView", clientInfo, requestParameters, e);
        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setErrorCode(ErrorCodeConstant.SYSTEM_ERROR);
            result.setErrorMsg("系统错误，请稍候重试！");
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "getRollbackView", clientInfo, requestParameters, e);

        }
        return result;
    }

    /***
     * 查询namespace发布详情
     * @param namespaceId namespaceId
     * @param clientInfo  api调用业务方名称 与授权
     * @return 版本详情
     */
    public Result<VersionItemView> rollbackVersion(String namespaceId, String fromVersion, String toVersion, ConsoleClientInfo clientInfo) {
        String requestParameters = "nsId=" + namespaceId + ";toVersion=" + toVersion;

        ServiceLoggerUtil.logServiceInput("NamespaceVersionService", "rollbackVersion", clientInfo, requestParameters);

        Result<VersionItemView> result = new Result<>();
        Long begin = null;
        try {
            if (!diamondCommonDataManager.openRollback()) {
                throw new BusinessException("回滚功能未打开", ErrorCodeConstant.PROHIBIT_OPERATION);

            }
            User user = clientInfo.getUser();
            if (user == null) {
                throw new BusinessException("operator is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }

            NamespaceBO namespaceBO = namespaceManager.selectAvailableByNamespaceId(namespaceId);
            checkPermission(namespaceBO, user);

            begin = System.currentTimeMillis();
            createVersionManager.lockNamespace(namespaceId);
            String version = rollbackVersionManager.rollBackVersion(namespaceBO, fromVersion, toVersion, clientInfo);


            VersionItemView itemView = new VersionItemView();
            itemView.setVersion(version);
            itemView.setNamespaceId(namespaceId);
            result.setModel(itemView);
            ServiceLoggerUtil.logServiceResult("NamespaceVersionService", "rollbackVersion", clientInfo, requestParameters, true);

        } catch (BusinessException e) {
            if (StringUtils.equalsIgnoreCase(ErrorCodeConstant.NAMESPACE_LOCK_ERROR, e.getErrorCode())) {
                begin = null;
            }
            result.setIsSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "rollbackVersion", clientInfo, requestParameters, e);
        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setErrorCode(ErrorCodeConstant.SYSTEM_ERROR);
            result.setErrorMsg("系统错误，请稍候重试！");
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "rollbackVersion", clientInfo, requestParameters, e);

        } finally {
            createVersionManager.unlockNamespace(namespaceId, begin);
        }
        return result;
    }

    /**
     * 查询指定版本发布影响的设备数
     *
     * @param namespaceId
     * @param version
     * @return
     */
    public long getDeviceCnt(String namespaceId, String version) {
        NamespaceVersionBO namespaceVersion = this.namespaceVersionManager.getAndCheckVersion(namespaceId, version);
        return this.massManager.searchDeviceCount(namespaceVersion, Duration.ofHours(24));
    }

    /***
     * Orange ChangeVersion架构调整后可回滚任一版本
     * 2022.02.23日
     * @param namespaceId
     * @param clientInfo
     * @return
     */
    public Result<List<NamespaceVersionBO>> getRollbackVersionList(String namespaceId, ConsoleClientInfo clientInfo) {
        String requestParameters = "nsId=" + namespaceId;

        ServiceLoggerUtil.logServiceInput("NamespaceVersionService", "getRollbackVersionList", clientInfo, requestParameters);

        Result<List<NamespaceVersionBO>> result = new Result<>();
        try {
            Preconditions.checkArgument(StringUtils.isNotBlank(namespaceId), "namespace is null");
            NamespaceBO namespaceBO = namespaceManager.selectAvailableByNamespaceId(namespaceId);
            if (namespaceBO == null) {
                throw new BusinessException("namespaceBO is null", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
            }
            rollbackVersionManager.checkCanRollbackAnyVersion(namespaceBO);
            List<NamespaceVersionBO> list = getPublishedListExcludeFirst(namespaceId);
            result.setModel(list);
            ServiceLoggerUtil.logServiceResult("NamespaceVersionService", "getRollbackVersionList", clientInfo, requestParameters, true);

        } catch (BusinessException e) {
            result.setIsSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getMessage());
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "getRollbackVersionList", clientInfo, requestParameters, e);
        } catch (Exception e) {
            result.setIsSuccess(false);
            result.setErrorCode(ErrorCodeConstant.SYSTEM_ERROR);
            result.setErrorMsg("系统错误，请稍候重试！");
            ServiceLoggerUtil.logServiceException("NamespaceVersionService", "getRollbackView", clientInfo, requestParameters, e);

        }
        return result;
    }

    private List<NamespaceVersionBO> getPublishedListExcludeFirst(String namespaceId) {
        List<NamespaceVersionBO> list = namespaceVersionManager.selectPublishedListByNamespaceIdDesc(namespaceId, 11);
        if (CollectionUtils.isNotEmpty(list)) {
            //去掉第一个最新的
            list.remove(0);
        }

        return list;
        //然后过滤仅余最近3个月的
        // Date minCreateDate = DateUtil.dateDiff(-30, Calendar.DATE);
        //return list.stream().filter(item -> item.getGmtCreate().after(minCreateDate)).collect(Collectors.toList());

    }

    private void checkPermission(NamespaceBO namespaceBO, User user) {
        boolean hasPermission = userManager.isOwnerAuth(namespaceBO, user, diamondCommonDataManager.isConsoleAdmin(user.getEmpId()));
        if (!hasPermission) {
            throw new BusinessException(String
                    .format("仅限namespace负责人查看, %s: %s", namespaceBO.getAppKeyOrGroup(), namespaceBO.getName()), ErrorCodeConstant.PROHIBIT_OPERATION);
        }

    }
}
