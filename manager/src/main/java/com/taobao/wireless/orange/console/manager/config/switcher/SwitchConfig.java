package com.taobao.wireless.orange.console.manager.config.switcher;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SwitchConfig {
    @AppSwitch(des = "启用快速配置链路", level = Switch.Level.p1)
    public static Boolean BOOT_NAMESPACE_ENABLE = true;

    @AppSwitch(des = "启用百分比灰度", level = Switch.Level.p1)
    public static Boolean RATIO_GRAY_ENABLE = false;

    @AppSwitch(des = "新版控制台前端版本", level = Switch.Level.p1)
    public static String NEW_FE_VERSION = "0.0.1";

    @AppSwitch(des = "推送配置给在线活跃设备最低支持应用版本", level = Switch.Level.p1)
    public static HashMap<String, String> APP_KEY_2_ACCS_PUSH_MIN_APP_VERSION_MAP = new HashMap<>();

    @AppSwitch(des = "支持快速发布的 APP 列表", level = Switch.Level.p2)
    public static List<String> SUPPORT_ACCELERATED_PUBLISH_APP_LIST = Arrays.asList("21380790", "21646297");

    @AppSwitch(des = "Changefree 核心应用列表", level = Switch.Level.p2)
    public static List<String> CHANGE_FREE_CORE_APP_LIST = Arrays.asList("21380790", "21646297", "34639004");

    @AppSwitch(des = "支持小流量灰度的应用最小版本号", level = Switch.Level.p2)
    public static HashMap<String, String> APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP = new HashMap<>();
}
