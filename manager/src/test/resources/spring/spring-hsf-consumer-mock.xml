<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
       ">

    <!-- 预案平台回调 -->
    <bean id="orangeNotifyService" class="com.taobao.wireless.orange.console.manager.mock.OrangeNotifyServiceMock"/>

    <!-- 工作流 -->
    <bean id="processInstanceService" class="com.taobao.wireless.orange.console.manager.mock.ProcessInstanceServiceMock"/>

    <!--diamond强制灰度amcc-->
    <bean id="diamondService" class="com.taobao.wireless.orange.console.manager.mock.DiamondServiceMock"/>
    <!--dp平台数据-->
    <bean id="dpDataCenterService" class="com.taobao.wireless.orange.console.manager.mock.DpDataCenterServiceMock"/>

    <!--Crash数据-->
    <bean id="crashDataCenterService" class="com.taobao.wireless.orange.console.manager.mock.CrashDataCenterServiceMock"/>


    <bean id="accessControlService" class="com.taobao.wireless.orange.console.manager.mock.AccessControlServiceMock"/>

    <bean id="permissionService" class="com.taobao.wireless.orange.console.manager.mock.PermissionServiceMock"/>

    <bean id="reportTrendOpenApi" class="com.taobao.wireless.orange.console.manager.mock.ReportTrendOpenApiMock"/>

    <bean id="reportBundleCountApi" class="com.taobao.wireless.orange.console.manager.mock.ReportBundleCountApiMock"/>

    <bean id="reportCountOpenApi" class="com.taobao.wireless.orange.console.manager.mock.ReportCountOpenApiMock"/>

    <!--应用中心-->
    <bean id="mappInfoReadConsoleService" class="com.taobao.wireless.orange.console.manager.mock.MappInfoReadConsoleServiceMock"/>

    <bean id="amdpDataQueryServiceMock" class="com.taobao.wireless.orange.console.manager.mock.AmdpDataQueryServiceMock"/>

</beans>