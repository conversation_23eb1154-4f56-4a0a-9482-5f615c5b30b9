<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="
	http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	">

    <bean id="namespaceApiService" class="com.taobao.wireless.orange.console.manager.api.impl.NamespaceApiServiceImpl" />
    <bean id="pushApiService" class="com.taobao.wireless.orange.console.manager.api.impl.PushApiServiceImpl" />
    <bean id="publishApiService" class="com.taobao.wireless.orange.console.manager.api.impl.PublishApiServiceImpl" />

    <bean id="openCustomService" class="com.taobao.wireless.orange.console.manager.api.impl.OpenCustomServiceImpl" />


</beans>