<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:lang="http://www.springframework.org/schema/lang"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
	http://www.springframework.org/schema/lang http://www.springframework.org/schema/lang/spring-lang-2.5.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

    <bean id="diamondConfigService" class="com.taobao.wireless.orange.core.service.impl.DiamondConfigServiceImpl"
          init-method="init">
        <property name="group" value="${orange.diamond.group}"/>
        <property name="dataId" value="${orange.diamond.dataId.common}"/>
    </bean>

    <bean id="accountConfigService" class="com.taobao.wireless.orange.core.service.impl.DiamondConfigServiceImpl"
          init-method="init">
        <property name="group" value="${orange.diamond.group}"/>
        <property name="dataId" value="${orange.diamond.dataId.accountConfig}"/>
    </bean>

    <bean id="orangeSwitches" class="com.taobao.wireless.orange.core.switches.OrangeSwitches" init-method="init"/>


    <bean id="diamondCommonDataManager" class="com.taobao.wireless.orange.console.manager.mock.DiamondCommonDataManagerMock" />

</beans>