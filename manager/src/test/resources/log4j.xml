<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">

<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">


  <appender name="consoleAppender" class="org.apache.log4j.ConsoleAppender">
    <param name="encoding" value="utf-8" />
    <layout class="org.apache.log4j.PatternLayout">
      <param name="ConversionPattern" value="[log4j] %d %-5p [%c:%L] >> %n%n -- %m %n%n" />
    </layout>
  </appender>


  <logger name="org.springframework" additivity="false">
    <level value="error" />
    <appender-ref ref="consoleAppender" />
  </logger>

  <logger name="com.ibatis" additivity="false">
    <level value="error" />
    <appender-ref ref="consoleAppender" />
  </logger>

  <logger name="java.sql" additivity="false">
    <level value="error" />
    <appender-ref ref="consoleAppender" />
  </logger>


  <logger name="com.taobao" additivity="false">
    <level value="error" />
    <appender-ref ref="consoleAppender" />
  </logger>

  <logger name="com.alibaba" additivity="false">
    <level value="error" />
    <appender-ref ref="consoleAppender" />
  </logger>
  
  <logger name="com.taobao.wireless.orange" additivity="false">
    <level value="debug" />
    <appender-ref ref="consoleAppender" />
  </logger>  
 

  <root>
    <level value="error" />
    <appender-ref ref="consoleAppender" />
  </root>

</log4j:configuration>
