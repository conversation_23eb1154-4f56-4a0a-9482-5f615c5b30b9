package com.taobao.wireless.orange.console.manager.expression;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class AndMatcherTest {

    @Test
    public void testAnd() {
        AndMatcher andMatcher = new AndMatcher(new UnitMatcher());
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("app_ver", "123");
        valueMap.put("os_ver", "321");
        assertTrue(andMatcher.compile("app_ver=123&os_ver=321").match(valueMap));
    }

    @Test
    public void testLackValue() {
        AndMatcher andMatcher = new AndMatcher(new UnitMatcher());
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("app_ver", "123");
        assertFalse(andMatcher.compile("app_ver=123&os_ver=321").match(valueMap));
    }

    @Test(expected = ExpressionParseException.class)
    public void testLimit() {
        AndMatcher andMatcher = new AndMatcher(new UnitMatcher());
        andMatcher.compile("app_ver=123&os_ver=321", 1);
    }

    @Test(expected = ExpressionParseException.class)
    public void testIn() {
        AndMatcher andMatcher = new AndMatcher(new UnitMatcher());
        andMatcher.compile("app_ver⊂9.3.1,9.3.0,9.2.9,9.2.8,9.2.7,9.2.6,9.1.8,9.1.7", 1);
    }

}
