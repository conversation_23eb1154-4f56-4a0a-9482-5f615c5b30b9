package com.taobao.wireless.orange.console.manager.support.tiga;

import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.core.service.model.NamespaceVersionBO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;

import static com.taobao.wireless.orange.console.manager.config.switcher.SwitchConfig.APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP;
import static org.junit.Assert.*;

/**
 * TigaManager.isSupportSmallGray方法的简化单元测试
 * 
 * 这个测试类使用实际的Spring上下文，避免了复杂的Mock设置
 * 主要测试各种业务场景下的返回值是否正确
 */
public class TigaManagerIsSupportSmallGraySimpleTest extends BaseManagerDebug {

    @Autowired
    private TigaManager tigaManager;

    private static final String TEST_APP_KEY = "testAppKey123";
    private static final String MIN_APP_VERSION = "10.0.0";
    
    // 保存原始配置，用于测试后恢复
    private HashMap<String, String> originalConfig;

    @Before
    public void setUp() {
        // 备份原始配置
        originalConfig = new HashMap<>(APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP);
        
        // 设置测试用的最小版本配置
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.put(TEST_APP_KEY, MIN_APP_VERSION);
    }

    @After
    public void tearDown() {
        // 恢复原始配置
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.clear();
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.putAll(originalConfig);
    }

    /**
     * 测试未配置最小版本的应用，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_NoMinVersionConfig() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO("unconfiguredAppKey", "");
        
        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        
        // Then
        assertFalse("未配置最小版本的应用应该返回false", result);
    }

    /**
     * 测试空策略（兜底策略），应该返回true
     */
    @Test
    public void testIsSupportSmallGray_EmptyStrategy() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "");
        
        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        
        // Then
        assertTrue("空策略应该返回true", result);
    }

    /**
     * 测试null策略，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_NullStrategy() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, null);
        
        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        
        // Then
        assertTrue("null策略应该返回true", result);
    }

    /**
     * 测试只包含空格和横线的策略，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_WhitespaceAndDashStrategy() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "  -  ");
        
        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        
        // Then
        assertTrue("只包含空格和横线的策略应该返回true", result);
    }

    /**
     * 测试包含大于等于操作符的策略，应该返回false
     * 注意：这个测试可能会因为StrategyExpression解析失败而抛异常
     */
    @Test
    public void testIsSupportSmallGray_StrategyWithGreaterEquals() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>=9.0.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertFalse("包含大于等于操作符的策略应该返回false", result);
        } catch (Exception e) {
            // 如果解析策略时抛异常，说明策略格式有问题，这也是一种不支持的情况
            System.out.println("策略解析异常，这也表示不支持小流量灰度: " + e.getMessage());
        }
    }

    /**
     * 测试包含大于操作符的策略，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_StrategyWithGreater() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>9.0.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertFalse("包含大于操作符的策略应该返回false", result);
        } catch (Exception e) {
            System.out.println("策略解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试包含小于等于操作符的策略，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_StrategyWithLessEquals() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver<=11.0.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertFalse("包含小于等于操作符的策略应该返回false", result);
        } catch (Exception e) {
            System.out.println("策略解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试包含小于操作符的策略，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_StrategyWithLess() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver<11.0.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertFalse("包含小于操作符的策略应该返回false", result);
        } catch (Exception e) {
            System.out.println("策略解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试版本值等于最小版本的情况，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_VersionEqualsMinVersion() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=10.0.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertTrue("版本值等于最小版本应该返回true", result);
        } catch (Exception e) {
            System.out.println("策略解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试版本值大于最小版本的情况，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_VersionGreaterThanMinVersion() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=11.0.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertTrue("版本值大于最小版本应该返回true", result);
        } catch (Exception e) {
            System.out.println("策略解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试版本值小于最小版本的情况，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_VersionLessThanMinVersion() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=9.0.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertFalse("版本值小于最小版本应该返回false", result);
        } catch (Exception e) {
            System.out.println("策略解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试非app_version维度的策略，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_NonAppVersionStrategy() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "os_ver>=14.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertTrue("非app_version维度的策略应该返回true", result);
        } catch (Exception e) {
            System.out.println("策略解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试混合策略（包含app_version和其他维度）
     */
    @Test
    public void testIsSupportSmallGray_MixedStrategy() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=10.5.0&os_ver>=14.0");
        
        try {
            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);
            
            // Then
            assertTrue("混合策略中app_version符合条件应该返回true", result);
        } catch (Exception e) {
            System.out.println("策略解析异常: " + e.getMessage());
        }
    }

    /**
     * 测试边界情况：最小版本配置为空字符串
     */
    @Test
    public void testIsSupportSmallGray_EmptyMinVersionConfig() {
        // Given
        String testAppKey = "emptyMinVersionApp";
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.put(testAppKey, "");
        NamespaceVersionBO versionBO = createVersionBO(testAppKey, "app_ver=10.0.0");
        
        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        
        // Then
        assertFalse("最小版本配置为空字符串应该返回false", result);
    }

    /**
     * 测试边界情况：最小版本配置为null
     */
    @Test
    public void testIsSupportSmallGray_NullMinVersionConfig() {
        // Given
        String testAppKey = "nullMinVersionApp";
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.put(testAppKey, null);
        NamespaceVersionBO versionBO = createVersionBO(testAppKey, "app_ver=10.0.0");
        
        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        
        // Then
        assertFalse("最小版本配置为null应该返回false", result);
    }

    /**
     * 创建NamespaceVersionBO对象的辅助方法
     */
    private NamespaceVersionBO createVersionBO(String appKey, String strategy) {
        NamespaceVersionBO versionBO = new NamespaceVersionBO();
        versionBO.setAppKey(appKey);
        versionBO.setStrategy(strategy);
        return versionBO;
    }
}
