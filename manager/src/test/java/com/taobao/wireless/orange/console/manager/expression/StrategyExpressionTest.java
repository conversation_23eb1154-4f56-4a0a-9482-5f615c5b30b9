package com.taobao.wireless.orange.console.manager.expression;

import com.google.common.collect.ImmutableList;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

public class StrategyExpressionTest {

    @Test
    public void testToQueryString() {
        doTestStrategyExpression(ImmutableList.<Pair<String, String>>builder()
                        .add(Pair.of("app_ver!=2.8.1", "(app_version!='2.8.1' and app_version!=null)"))
                        .add(Pair.of("app_ver=2.8.1", "app_version='2.8.1'"))
                        .add(Pair.of("app_ver=2.8.1&did_hash=100_0-1", "app_version='2.8.1'"))
                        .add(Pair.of("app_ver!=2.8.1&m_model=XIAOMI", "(app_version!='2.8.1' and app_version!=null) and model='XIAOMI'"))
                        .add(Pair.of("app_ver!=2.8.1&m_model!~XIAOMI", "(app_version!='2.8.1' and app_version!=null) and (model!~'XIAOMI' and model!=null)"))
                        .add(Pair.of("app_ver>=2.8.1&os_ver=2.0", "app_version>='2.8.1' and os_version='2.0'"))
                        .add(Pair.of("app_ver>1&did_hash=100_0-1", "app_version>'1'"))
                        .add(Pair.of("os_ver>=1&os_ver>=1", "os_version>='1' and os_version>='1'"))
                        .add(Pair.of("m_model<12 45&m_brand~=V1814", "model<'12 45' and brand~='V1814'"))
                        .add(Pair.of("m_model<=12 45&m_brand!~V1814", "model<='12 45' and (brand!~'V1814' and brand!=null)"))
                        .add(Pair.of("did_hash=100_0-1", ""))
                        .add(Pair.of("app_ver>=8.11.0&os_ver=21&m_model~=SM-N900", "app_version>='8.11.0' and os_version='21' and model~='SM-N900'"))
                        .build(),
                strategyExpression -> strategyExpression.toQueryString(getFormatFunction()));
    }

    private static void doTestStrategyExpression(List<Pair<String, String>> testAndResults, Function<StrategyExpression, String> function) {
        testAndResults.forEach(testAndResult -> {
            StrategyExpression strategyExpression = new StrategyExpression(testAndResult.getLeft());
            Assert.assertEquals(testAndResult.getRight(), function.apply(strategyExpression));
        });
    }

    @Test
    public void testNot() {
        doTestStrategyExpression(ImmutableList.<Pair<String, String>>builder()
                .add(Pair.of("app_ver>1&did_hash=100_0-1", "app_version<='1'"))
                .add(Pair.of("os_ver>=1&os_ver>=1", "os_version<'1' or os_version<'1'"))
                .add(Pair.of("m_model<12 45&m_brand~=V1814", "model>='12 45' or (brand!~'V1814' and brand!=null)"))
                .add(Pair.of("m_model<=12 45&m_brand!~V1814", "model>'12 45' or brand~='V1814'"))
                .add(Pair.of("did_hash=100_0-1", ""))
                .add(Pair.of("app_ver>=8.11.0&os_ver=21&m_model~=SM-N900", "app_version<'8.11.0' or (os_version!='21' and os_version!=null) or (model!~'SM-N900' and model!=null)"))
                .build(), strategyExpression -> strategyExpression.not().toQueryString(getFormatFunction()));
    }

    @Test
    public void testCompileError() {
        Arrays.asList(null, "agsge=22").forEach(errorExpr -> {
            assertParseException(errorExpr);
        });
    }

    private static void assertParseException(String expression) {
        boolean parseSuccess = true;
        try {
            new StrategyExpression(expression);
        } catch (Exception e) {
            parseSuccess = false;
        }
        Assert.assertFalse(parseSuccess);
    }

    static Function<OpExpr, String> getFormatFunction() {
        return opExpr -> {
            String normalFormatStr = String.format("%s%s'%s'", opExpr.getKey(), opExpr.getOpr().getSymbol(), opExpr.getVal());
            if (!opExpr.getOpr().isNotOp()) {
                return normalFormatStr;
            }
            String notNullFormatStr = String.format("%s!=null", opExpr.getKey());
            String joinSymbol = "and";
            return String.format("(%s %s %s)", normalFormatStr, joinSymbol, notNullFormatStr);
        };
    }
}
