package com.taobao.wireless.orange.console.manager.support;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.taobao.wireless.orange.console.manager.BaseMockUnitManager;
import com.taobao.wireless.orange.console.manager.support.diamond.AccountConfigManager;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;

public class OrangeConsleOssDebug extends BaseMockUnitManager {

    @Autowired
    private AccountConfigManager accountConfigManager;

    private OSS client;
    private String bucketName;

    @Before
    public void initOss() {
        OSSClientBuilder ossClientBuilder = new OSSClientBuilder();
        client = ossClientBuilder.build(
                accountConfigManager.getCdnOssConfig().getEndpoint(),
                accountConfigManager.getCdnOssConfig().getAccessKey(),
                accountConfigManager.getCdnOssConfig().getAccessSecret());
        bucketName = accountConfigManager.getCdnOssConfig().getBucketName();
    }

    @Test
//    @Ignore
    public void pushDebugPageToDomain() {
        String ossKey = "debug/v2/index.html";
        initOss();
        setMetaData(ossKey);
        getFile(ossKey);
    }

    private OSSObject getFile(String ossKey) {
        OSSObject ossObject = client.getObject(bucketName, ossKey);
        if (ossObject != null) {
            System.out.println(ossObject.getObjectMetadata().getContentType());
            System.out.println(ossObject.getObjectMetadata().getContentDisposition());
        }
        return ossObject;
    }

    private void setMetaData(String ossKey) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentDisposition(null);
        metadata.setContentType("text/html");
        File file = new File("/Users/<USER>/ws/js/orangeview/debug/index.html");
        Assert.assertTrue(file.exists());
        client.putObject(bucketName, ossKey, file, metadata);
    }
}
