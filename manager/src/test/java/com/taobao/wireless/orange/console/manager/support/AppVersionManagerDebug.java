package com.taobao.wireless.orange.console.manager.support;

import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.console.manager.AppVersionManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class AppVersionManagerDebug extends BaseManagerDebug {

    @Autowired
    private AppVersionManager appVersionManager;


    private final String appKey = "60023163";


    @Test
    public void test() throws Exception {


        appVersionManager.queryAppVersionForDeploy(appKey);


    }


    @Test
    public void test2() {


        appVersionManager.queryAppVersionFroOtherDeploy(appKey, true);
    }

    @Override
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {

    }
}
