package com.taobao.wireless.orange.console.manager.expression;

import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2020/03/19
 */
public class StrategyExprCompilerTest {
    @Test
    public void testCompile() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
        Assert.assertNull(logicExpr);
        expr = "app_ver>1&did_hash=100_0-1";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>1&app_ver>1";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>10";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>10&os_ver=9";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>10&os_ver=9&m_brand=OPPO";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>10&os_ver=9&m_brand=OPPO&m_model=EVA-100";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>10&os_ver=9&m_brand=OPPO&m_model=EVA-100&did_hash=100_0-1";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>10&os_ver=9&m_brand=OPPO&m_model=EVA-100&did_hash=100_0-1&customKey1=11";
        logicExpr = compiler.compile(expr, Sets.newHashSet("customKey1"));
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>10&os_ver=9&m_brand=OPPO&m_model=EVA-100&did_hash=100_0-1&customKey1=11&customKey2=2";
        logicExpr = compiler.compile(expr, Sets.newHashSet("customKey1", "customKey2"));
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver>10&os_ver=9&m_brand=OPPO&m_model=EVA-100&did_hash=100_0-1&customKey1=11&customKey2=2";
        logicExpr = compiler.compile(expr, Sets.newHashSet("customKey1", "customKey2"));
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "m_model=EVA  100";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
        expr = "app_ver=1.3&m_model=EVA  100";
        logicExpr = compiler.compile(expr);
        Assert.assertEquals(expr, logicExpr.toString());
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileLimit() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "app_ver>10&os_ver=9&m_brand=OPPO&m_model=EVA-100";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileSpace() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "app_ver >10&os_ver=9&m_brand=OPPO&m_model=EVA-100";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileUnknownKey() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "unknown >10&os_ver=9&m_brand=OPPO&m_model=EVA-100";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileWhitespaceKey() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "os_ver =9&m_brand=OPPO&m_model=EVA-100";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileWhitespaceKey1() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "os_ver= 9&m_brand=OPPO&m_model=EVA-100";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileWhitespaceKey3() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "os_ver=9 &m_brand=OPPO&m_model=EVA-100";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileWhitespaceKey4() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "os_ver=9& m_brand=OPPO&m_model=EVA-100";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileVersionWhitespaceKey4() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "os_ver=9. 6&m_brand=O PPO&m_model=EVA-100";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }

    @Test(expected = ExpressionParseException.class)
    public void testCompileDidHashWhitespaceKey4() {
        StrategyExprCompiler compiler = new StrategyExprCompiler();
        String expr = "os_ver=9.6&did_hash=100 _0-1";
        LogicExpr logicExpr = compiler.compile(expr, 3, null);
    }
}