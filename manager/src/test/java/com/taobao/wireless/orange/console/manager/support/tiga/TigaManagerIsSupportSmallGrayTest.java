package com.taobao.wireless.orange.console.manager.support.tiga;

import com.taobao.wireless.orange.console.manager.expression.OpExpr;
import com.taobao.wireless.orange.console.manager.expression.StrategyExpression;
import com.taobao.wireless.orange.core.service.model.NamespaceVersionBO;
import com.taobao.wireless.tiga.release.expression.Field;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static com.taobao.wireless.orange.console.manager.config.switcher.SwitchConfig.APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 单元测试类：TigaManager.isSupportSmallGray方法
 *
 * 测试场景包括：
 * 1. 未配置最小版本的应用
 * 2. 空策略（兜底策略）
 * 3. 包含大于操作符的策略
 * 4. 包含小于操作符的策略
 * 5. 版本比较的各种情况
 */
@RunWith(MockitoJUnitRunner.class)
public class TigaManagerIsSupportSmallGrayTest {

    @InjectMocks
    private TigaManager tigaManager;

    private static final String TEST_APP_KEY = "testAppKey";
    private static final String MIN_APP_VERSION = "10.0.0";

    @Before
    public void setUp() {
        // 设置测试用的最小版本配置
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.clear();
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.put(TEST_APP_KEY, MIN_APP_VERSION);
    }

    /**
     * 测试未配置最小版本的应用，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_NoMinVersionConfig() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO("unconfiguredAppKey", "");

        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);

        // Then
        assertFalse("未配置最小版本的应用应该返回false", result);
    }

    /**
     * 测试空策略（兜底策略），应该返回true
     */
    @Test
    public void testIsSupportSmallGray_EmptyStrategy() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "");

        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);

        // Then
        assertTrue("空策略应该返回true", result);
    }

    /**
     * 测试null策略，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_NullStrategy() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, null);

        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);

        // Then
        assertTrue("null策略应该返回true", result);
    }

    /**
     * 测试包含大于等于操作符的策略，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_StrategyWithGreaterEquals() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>=9.0.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr opExpr = createOpExpr("app_version", OpExpr.Operator.GREATER_EQUALS, "9.0.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(opExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertFalse("包含大于等于操作符的策略应该返回false", result);
            }
        }
    }

    /**
     * 测试包含大于操作符的策略，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_StrategyWithGreater() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>9.0.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr opExpr = createOpExpr("app_version", OpExpr.Operator.GREATER, "9.0.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(opExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertFalse("包含大于操作符的策略应该返回false", result);
            }
        }
    }

    /**
     * 测试包含小于等于操作符的策略，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_StrategyWithLessEquals() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver<=11.0.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr opExpr = createOpExpr("app_version", OpExpr.Operator.LESS_EQUALS, "11.0.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(opExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertFalse("包含小于等于操作符的策略应该返回false", result);
            }
        }
    }

    /**
     * 测试包含小于操作符的策略，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_StrategyWithLess() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver<11.0.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr opExpr = createOpExpr("app_version", OpExpr.Operator.LESS, "11.0.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(opExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertFalse("包含小于操作符的策略应该返回false", result);
            }
        }
    }

    /**
     * 测试版本值小于最小版本的情况，应该返回false
     */
    @Test
    public void testIsSupportSmallGray_VersionLessThanMinVersion() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=9.0.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr opExpr = createOpExpr("app_version", OpExpr.Operator.EQUALS, "9.0.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(opExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertFalse("版本值小于最小版本应该返回false", result);
            }
        }
    }

    /**
     * 测试版本值等于最小版本的情况，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_VersionEqualsMinVersion() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=10.0.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr opExpr = createOpExpr("app_version", OpExpr.Operator.EQUALS, "10.0.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(opExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertTrue("版本值等于最小版本应该返回true", result);
            }
        }
    }

    /**
     * 测试版本值大于最小版本的情况，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_VersionGreaterThanMinVersion() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=11.0.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr opExpr = createOpExpr("app_version", OpExpr.Operator.EQUALS, "11.0.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(opExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertTrue("版本值大于最小版本应该返回true", result);
            }
        }
    }

    /**
     * 测试非app_version维度的策略，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_NonAppVersionStrategy() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "os_ver>=14.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr opExpr = createOpExpr("os_version", OpExpr.Operator.GREATER_EQUALS, "14.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(opExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertTrue("非app_version维度的策略应该返回true", result);
            }
        }
    }

    /**
     * 测试混合策略（包含app_version和其他维度），app_version符合条件
     */
    @Test
    public void testIsSupportSmallGray_MixedStrategyWithValidAppVersion() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=10.5.0&os_ver>=14.0");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);
            OpExpr appVersionOpExpr = createOpExpr("app_version", OpExpr.Operator.EQUALS, "10.5.0");
            OpExpr osVersionOpExpr = createOpExpr("os_version", OpExpr.Operator.GREATER_EQUALS, "14.0");

            when(strategyExpression.getParsedOpList()).thenReturn(Arrays.asList(appVersionOpExpr, osVersionOpExpr));
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // Mock Field.APP_VERSION.getValue()
            try (MockedStatic<Field> mockedField = Mockito.mockStatic(Field.class)) {
                Field appVersionField = Mockito.mock(Field.class);
                when(appVersionField.getValue()).thenReturn("app_version");
                mockedField.when(() -> Field.APP_VERSION).thenReturn(appVersionField);

                // When
                boolean result = tigaManager.isSupportSmallGray(versionBO);

                // Then
                assertTrue("混合策略中app_version符合条件应该返回true", result);
            }
        }
    }

    /**
     * 测试空的OpExpr列表，应该返回true
     */
    @Test
    public void testIsSupportSmallGray_EmptyOpExprList() {
        // Given
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "did_hash=100_0-1");

        // Mock StrategyExpression 和相关对象
        try (MockedStatic<StrategyExpression> mockedStrategyExpression = Mockito.mockStatic(StrategyExpression.class)) {
            StrategyExpression strategyExpression = Mockito.mock(StrategyExpression.class);

            when(strategyExpression.getParsedOpList()).thenReturn(Collections.emptyList());
            mockedStrategyExpression.when(() -> new StrategyExpression(anyString(), any(), any(), any()))
                    .thenReturn(strategyExpression);

            // When
            boolean result = tigaManager.isSupportSmallGray(versionBO);

            // Then
            assertTrue("空的OpExpr列表应该返回true", result);
        }
    }

    /**
     * 测试边界情况：最小版本配置为空字符串
     */
    @Test
    public void testIsSupportSmallGray_EmptyMinVersionConfig() {
        // Given
        String testAppKey = "emptyMinVersionApp";
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.put(testAppKey, "");
        NamespaceVersionBO versionBO = createVersionBO(testAppKey, "app_ver=10.0.0");

        // When
        boolean result = tigaManager.isSupportSmallGray(versionBO);

        // Then
        assertFalse("最小版本配置为空字符串应该返回false", result);
    }

    /**
     * 创建NamespaceVersionBO对象的辅助方法
     */
    private NamespaceVersionBO createVersionBO(String appKey, String strategy) {
        NamespaceVersionBO versionBO = new NamespaceVersionBO();
        versionBO.setAppKey(appKey);
        versionBO.setStrategy(strategy);
        return versionBO;
    }

    /**
     * 创建OpExpr对象的辅助方法
     */
    private OpExpr createOpExpr(String key, OpExpr.Operator operator, String value) {
        OpExpr opExpr = new OpExpr();
        opExpr.setKey(key);
        opExpr.setOpr(operator);
        opExpr.setVal(value);
        return opExpr;
    }
}
