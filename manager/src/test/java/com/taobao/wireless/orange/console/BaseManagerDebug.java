package com.taobao.wireless.orange.console;

import com.taobao.hsf.standalone.HSFEasyStarter;
import com.taobao.wireless.orange.console.manager.BaseMockUnitManager;
import com.taobao.wireless.orange.console.manager.api.model.client.OrangeClientInfo;
import com.taobao.wireless.orange.console.manager.model.User;
import com.taobao.wireless.orange.console.manager.service.ConsoleClientInfo;
import com.taobao.wireless.orange.core.type.Source;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:orange-console/manager/applicationContext.xml",
        "classpath:env.properties.debug.xml"})
public abstract class BaseManagerDebug {

    private static final AtomicBoolean start = new AtomicBoolean();

    protected OrangeClientInfo apiClientInfo = new OrangeClientInfo("fregata", "d88654b940810044");


    public static final String TEST_EMP_ID = BaseMockUnitManager.TEST_EMP_ID;
    public static final String TEST_ACCOUNT = BaseMockUnitManager.TEST_ACCOUNT;
    public static final String TEST_NICK_NAME = BaseMockUnitManager.TEST_NICK_NAME;

    protected static final String operator = TEST_EMP_ID;

    public static final User user;

    static {
        user = new User();
        user.setEmpId(TEST_EMP_ID);
        user.setNickNameCn(TEST_NICK_NAME);
        user.setEmailPrefix(TEST_ACCOUNT);
        user.setSupervisorEmpId("24523");
    }

    public static ConsoleClientInfo consoleClientInfo = new ConsoleClientInfo(user, Source.ORANGE);

    protected BaseManagerDebug() {
        System.out.println("启动hsf单元测试服务.....");
        String accsServicePluginPath = getClass().getResource("/accs-service-plugin").getPath();
        System.out.println("检查到accs-service-plugin路径:" + accsServicePluginPath);
        System.setProperty("com.taobao.pandora.plugins.ext_path", accsServicePluginPath);

        if (start.compareAndSet(false, true)) {
            HSFEasyStarter.start("", "");
        }

        System.out.println("hsf启动完毕 .....");
    }

    @Deprecated
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {
    }

}