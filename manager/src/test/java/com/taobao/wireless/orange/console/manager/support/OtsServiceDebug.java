package com.taobao.wireless.orange.console.manager.support;

import com.alibaba.fastjson.JSON;
import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.console.manager.manager.model.report.DsQueryMode;
import com.taobao.wireless.orange.console.manager.manager.report.DataCenterStatisticsManager;
import com.taobao.wireless.orange.console.manager.model.report.DataCenterStatisticsDD;
import com.taobao.wireless.orange.console.manager.support.ots.OtsHelper;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by kaici.zkc on 2020/6/1
 */
public class OtsServiceDebug extends BaseManagerDebug {

    @Autowired
    private OtsHelper otsManager;

    @Autowired
    private DataCenterStatisticsManager dataCenterStatisticsManager;

    @Test
    public void testSelectOne() {
        DataCenterStatisticsDD query = new DataCenterStatisticsDD();
        query.setAppKey("21380790");
        query.setDs("20200530");
        DataCenterStatisticsDD result = otsManager.select(query);
        System.out.println(result);
    }

    @Test
    public void testRangeQuery() {
        DataCenterStatisticsDD first = new DataCenterStatisticsDD();
        first.setAppKey("21380790");
        first.setDs("20200527");

        DataCenterStatisticsDD end = new DataCenterStatisticsDD();
        end.setAppKey("21380790");
        end.setDs("20200531");

        List<DataCenterStatisticsDD> dataCenterStatisticsDDS = otsManager.selectAll(first, end, 10).getResult();
        System.out.println(dataCenterStatisticsDDS.size());
        dataCenterStatisticsDDS.forEach(v -> System.out.println(v));

    }

    @Test
    public void test() {
        System.out.println(dataCenterStatisticsManager.getOtsDataCenterDDData("12381755", "20200622"));
    }

    @Test
    public void testDD() {
        System.out.println(dataCenterStatisticsManager.getOtsDataCenterHHDataList("12381755", "2020062210", "2020062215"));
        System.out.println(dataCenterStatisticsManager.getOtsDataCenterDDDataList("12381755", "20200625", "20200628"));
    }

    @Test
    public void testTrend() {
        System.out.println(JSON.toJSONString(dataCenterStatisticsManager.getAppTrendView("12381755", "2020062301", "2020062306", DsQueryMode.HH)));
    }

    @Test
    public void testView() {
        System.out.println(JSON.toJSONString(dataCenterStatisticsManager.getAppStatistics("12381755", "20200617")));
    }

    @Override
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {

    }
}
