package com.taobao.wireless.orange.console.manager.expression;

import org.junit.Assert;
import org.junit.Test;

/**
 * 操作符测试
 */
public class OperatorTest {

    @Test
    public void testIsOverlap() {
        String minVersion = "10.7.0";

        // 测试 ">"
        Assert.assertTrue(OpExpr.Operator.GREATER.isOverlap(OpExpr.ValueType.VERSION, "10.7.0", minVersion));
        Assert.assertTrue(OpExpr.Operator.GREATER.isOverlap(OpExpr.ValueType.VERSION, "10.7.1", minVersion));
        Assert.assertTrue(OpExpr.Operator.GREATER.isOverlap(OpExpr.ValueType.VERSION, "8.6.9", minVersion));

        // 测试 ">="
        Assert.assertTrue(OpExpr.Operator.GREATER_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.7.0", minVersion));
        Assert.assertTrue(OpExpr.Operator.GREATER_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.7.1", minVersion));
        Assert.assertTrue(OpExpr.Operator.GREATER_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "8.6.9", minVersion));

        // 测试 "<"
        Assert.assertTrue(OpExpr.Operator.LESS.isOverlap(OpExpr.ValueType.VERSION, "10.7.1", minVersion));
        Assert.assertFalse(OpExpr.Operator.LESS.isOverlap(OpExpr.ValueType.VERSION, "10.7.0", minVersion));
        Assert.assertFalse(OpExpr.Operator.LESS.isOverlap(OpExpr.ValueType.VERSION, "8.6.9", minVersion));

        // 测试 "<="
        Assert.assertTrue(OpExpr.Operator.LESS_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.7.1", minVersion));
        Assert.assertTrue(OpExpr.Operator.LESS_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.7.0", minVersion));
        Assert.assertFalse(OpExpr.Operator.LESS_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "8.6.9", minVersion));

        // 测试 "="
        Assert.assertTrue(OpExpr.Operator.EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.7.0", minVersion));
        Assert.assertTrue(OpExpr.Operator.EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.7.1", minVersion));
        Assert.assertFalse(OpExpr.Operator.EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.6.0", minVersion));

        // 测试 "!="
        Assert.assertTrue(OpExpr.Operator.NOT_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.7.1", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "10.7.0", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_EQUALS.isOverlap(OpExpr.ValueType.VERSION, "8.6.9", minVersion));

        // 测试 "~="
        Assert.assertTrue(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.8.0", minVersion));
        Assert.assertTrue(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.7.1", minVersion));
        Assert.assertTrue(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.7.0", minVersion));
        Assert.assertTrue(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.7", minVersion));
        Assert.assertTrue(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10", minVersion));
        Assert.assertTrue(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "1", minVersion));
        Assert.assertFalse(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.6.9", minVersion));
        Assert.assertFalse(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "8.6.9", minVersion));
        Assert.assertFalse(OpExpr.Operator.FUZZY.isOverlap(OpExpr.ValueType.VERSION, "1.0.7", minVersion));

        // 测试 "~="
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.8.0", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.7.1", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.7.0", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.7", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "1", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "10.6.9", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "8.6.9", minVersion));
        Assert.assertTrue(OpExpr.Operator.NOT_FUZZY.isOverlap(OpExpr.ValueType.VERSION, "1.0.7", minVersion));
    }

}