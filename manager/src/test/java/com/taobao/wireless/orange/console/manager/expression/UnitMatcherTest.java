package com.taobao.wireless.orange.console.manager.expression;

import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class UnitMatcherTest {

    @Test
    public void testEquals() {
        UnitMatcher um = new UnitMatcher();
        //字母数字结合
        String exp = "app_ver=test1";
        OpExpr opExpr = um.compile(exp);
        System.out.println(opExpr);
        assertTrue(opExpr.match("test1"));
        //纯数字
        exp = "app_ver=1234";
        opExpr = um.compile(exp);
        System.out.println(opExpr);
        assertTrue(opExpr.match("1234"));
        //空格干扰
//        exp = "  app_ver  =  1234     ";
//        opExpr = um.compile(exp);
//        System.out.println(opExpr);
//        assertTrue(opExpr.match("1234"));
        //内部空格保留
//        exp = "  app_ver  =  12 34     ";
//        opExpr = um.compile(exp);
//        System.out.println(opExpr);
//        assertTrue(opExpr.match("12 34"));

    }

    @Test
    public void testHash() {
        UnitMatcher um = new UnitMatcher();
        //hash
        String exp = "did_hash=10_1-2";
        OpExpr opExpr = um.compile(exp);
        assertFalse(opExpr.match("0"));
        assertFalse(opExpr.match("2"));
    }

    @Test
    public void testGreater() {
        UnitMatcher um = new UnitMatcher();
        //Version
        assertTrue(um.compile("os_ver>1.0.1").match("1.1.0"));
        //长度不一致的版本，更长的版本更大
        assertTrue(um.compile("os_ver>1.0").match("1.0.1"));
        assertTrue(um.compile("os_ver>1.0").match("1.0.0"));
        //含有字符的版本号，解析不会失败
        assertTrue(um.compile("os_ver>1.0.1-RC1").match("1.0.1-RC2"));
        //String
        assertTrue(um.compile("app_ver>123a").match("123b"));
        assertTrue(um.compile("app_ver>123").match("124"));
    }

    @Test
    public void testGreaterEqual() {
        UnitMatcher um = new UnitMatcher();
        //Version
        assertTrue(um.compile("os_ver>=1.0.1").match("1.1.0"));
        assertTrue(um.compile("os_ver>=1.0.1").match("1.0.1"));
        //长度不一致的版本，更长的版本更大
        assertTrue(um.compile("os_ver>=1.0").match("1.0.1"));
        assertTrue(um.compile("os_ver>=1.0").match("1.0.0"));
        //含有字符的版本号，解析不会失败
        assertTrue(um.compile("os_ver>=1.0.1-RC1").match("1.0.1-RC2"));
        //String
        assertTrue(um.compile("app_ver>=123a").match("123b"));
        assertTrue(um.compile("app_ver>=123a").match("123a"));
        assertTrue(um.compile("app_ver>=123").match("124"));
        assertTrue(um.compile("app_ver>=123").match("123"));
    }

    @Test
    public void testLess() {
        UnitMatcher um = new UnitMatcher();
        //Version
        assertTrue(um.compile("os_ver<1.0.1").match("1.0.0"));
        //长度不一致的版本，更长的版本更大
        assertTrue(um.compile("os_ver<1.0.1").match("1.0"));
        //含有字符的版本号，解析不会失败
        assertTrue(um.compile("os_ver<1.0.1-RC2").match("1.0.1-RC1"));
        //String
        assertTrue(um.compile("os_ver<123b").match("123a"));
        assertTrue(um.compile("os_ver<123").match("122"));
    }

    @Test
    public void testLessEqual() {
        UnitMatcher um = new UnitMatcher();
        //Version
        assertTrue(um.compile("os_ver<=1.0.1").match("1.0.0"));
        assertTrue(um.compile("os_ver<=1.0.1").match("1.0.1"));
        //长度不一致的版本，更长的版本更大
        assertTrue(um.compile("os_ver<=1.0.1").match("1.0"));
        assertTrue(um.compile("os_ver<=1.0.1").match("1.0.1"));
        //含有字符的版本号，解析不会失败
        assertTrue(um.compile("os_ver<=1.0.1-RC2").match("1.0.1-RC1"));
        assertTrue(um.compile("os_ver<=1.0.1-RC2").match("1.0.1-RC2"));
        //String
        assertTrue(um.compile("os_ver<=123b").match("123a"));
        assertTrue(um.compile("os_ver<=123b").match("123b"));
        assertTrue(um.compile("os_ver<=123").match("122"));
        assertTrue(um.compile("os_ver<=123").match("123"));
    }

    @Test
    public void testNotEqual() {
        UnitMatcher um = new UnitMatcher();
        //version
        assertTrue(um.compile("os_ver!=1.0.1").match("1.0.2"));
        assertFalse(um.compile("os_ver!=1.0.1").match("1.0.1"));

        //string
        assertTrue(um.compile("os_ver!=123").match("123abc"));
        assertFalse(um.compile("os_ver!=123abc").match("123abc"));
    }

    @Test
    public void testFuzzy() {
        UnitMatcher um = new UnitMatcher();
        assertTrue(um.compile("os_ver~=1.0").match("1.0.2"));
        assertTrue(um.compile("os_ver~=8.").match("8.1.1"));
        assertTrue(um.compile("os_ver~=abc").match("abc"));
    }

    @Test
    public void testNotFuzzy() {
        UnitMatcher um = new UnitMatcher();
        assertTrue(um.compile("os_ver!~1.0").match("2.0.2"));
        assertFalse(um.compile("os_ver!~1.0").match("1.0.2"));
    }
}
