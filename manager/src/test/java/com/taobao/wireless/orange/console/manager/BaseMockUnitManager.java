package com.taobao.wireless.orange.console.manager;


import com.taobao.wireless.orange.console.manager.api.model.client.OrangeClientInfo;
import com.taobao.wireless.orange.console.manager.model.User;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.List;

/***
 * 去掉 HSF、Metaq、Tair等的依赖
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.test.xml",
        "classpath:env.properties.test.xml"})
public class BaseMockUnitManager {

    public static final OrangeClientInfo clientInfo = new OrangeClientInfo("orange-console", "1335");
    public static final String TEST_EMP_ID = "39753";
    public static final String TEST_ACCOUNT = "lanyin.smz";
    public static final String TEST_SYSTEM = "system";
    public static final String TEST_NICK_NAME = "兰茵";
    public static final String TEST_APP_KEY = "********";
    public static final List<String> TEST_OPERATOR_LIST = Arrays.asList(TEST_EMP_ID, TEST_ACCOUNT, TEST_SYSTEM, TEST_NICK_NAME);

    public static final User user = new User();

    static {
        user.setEmpId(TEST_EMP_ID);
        user.setNickNameCn(TEST_NICK_NAME);
        user.setEmailAddr(TEST_ACCOUNT);
    }

}
