package com.taobao.wireless.orange.console.manager.expression;

import com.google.common.collect.ImmutableList;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

public class StrategyExpressionCombineTest {

    @Test
    public void testCompileCombine() {
        ImmutableList<String> testStrategies = ImmutableList.<String>builder()
                .add("app_ver>1&did_hash=100_0-1")
                .add("os_ver>=1&os_ver>=1")
                .add("m_model<12 45&m_brand~=V1814")
                .add("m_model<=12 45&m_brand!~V1814")
                .add("did_hash=100_0-1")
                .add("app_ver>=8.11.0&os_ver=21&m_model~=SM-N900")
                .build();
        StrategyExpressionCombine strategyExpressionCombine = new StrategyExpressionCombine(testStrategies);
        Assert.assertEquals(strategyExpressionCombine.toQueryString(StrategyExpressionTest.getFormatFunction()),
                "(app_version>'1') or (os_version>='1' and os_version>='1') or (model<'12 45' and brand~='V1814')" +
                        " or (model<='12 45' and (brand!~'V1814' and brand!=null)) or (app_version>='8.11.0' and os_version='21' and model~='SM-N900')");
        Assert.assertEquals(strategyExpressionCombine.not().toQueryString(StrategyExpressionTest.getFormatFunction()),
                "(app_version<='1') and (os_version<'1' or os_version<'1') and (model>='12 45' or (brand!~'V1814' and brand!=null)) and (model>'12 45' or brand~='V1814') and (app_version<'8.11.0' or (os_version!='21' and os_version!=null) or (model!~'SM-N900' and model!=null))");
    }

    @Test
    public void testToNotNullQueryString() {
        testStrategyExpressionCombine(ImmutableList.<Pair<List<String>, String>>builder()
                        // 测试单个策略
                        .add(Pair.of(Collections.singletonList("app_ver>9.0"), "app_version<='9.0'"))
                        .add(Pair.of(Collections.singletonList("app_ver!=9.0"), "app_version='9.0'"))
                        .add(Pair.of(Collections.singletonList("app_ver=9.0"), "(app_version!='9.0' and app_version!=null)"))
                        .add(Pair.of(Collections.singletonList("m_brand~=XIAOMI"), "(brand!~'XIAOMI' and brand!=null)"))
                        // 测试 did_hash
                        .add(Pair.of(Arrays.asList("did_hash=100_0-1", "did_hash=100_1-2"), ""))
                        // 测试 did_hash 和 其他维度
                        .add(Pair.of(Arrays.asList("did_hash=100_0-1", "app_ver=10.7.0"), "(app_version!='10.7.0' and app_version!=null)"))
                        // 测试 取反带有 != 和 !~ 的策略表达式
                        .add(Pair.of(Arrays.asList("app_ver=10.7.10", "app_ver>=9.0.0", "m_brand~=XIAOMI&app_ver>=10.3.0"),
                                "((app_version!='10.7.10' and app_version!=null)) and (app_version<'9.0.0') and ((brand!~'XIAOMI' and brand!=null) or app_version<'10.3.0')"))
                        // 测试 取反后不带有 != 和 !~ 的策略表达式
                        .add(Pair.of(Arrays.asList("app_ver!=10.7.10", "app_ver>=9.0.0&os_ver<10"), "(app_version='10.7.10') and (app_version<'9.0.0' or os_version>='10')"))
                        .build(),
                strategyExpressionCombine -> strategyExpressionCombine.not().toQueryString(StrategyExpressionTest.getFormatFunction()));
    }

    private static void testStrategyExpressionCombine(List<Pair<List<String>, String>> testDataAndResults, Function<StrategyExpressionCombine, String> testFunction) {
        testDataAndResults.forEach(testDataAndResult -> {
            StrategyExpressionCombine strategyExpressionCombine = new StrategyExpressionCombine(testDataAndResult.getLeft());
            Assert.assertEquals(testDataAndResult.getRight(), testFunction.apply(strategyExpressionCombine));
        });
    }
}
