package com.taobao.wireless.orange.console.manager.support;

import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.console.manager.AppManager;
import com.taobao.wireless.orange.console.manager.support.acl.OrangeAclAOHSF;
import com.taobao.wireless.orange.console.manager.support.acl.OrangeAclAOHTTP;
import com.taobao.wireless.orange.console.manager.support.acl.OrangeAclManager;
import com.taobao.wireless.orange.console.manager.support.acl.OrangeAclUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class OrangeAclManagerDebug extends BaseManagerDebug {
    @Override
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {

    }

    @Autowired
    private OrangeAclManager orangeAclManager;

    @Autowired
    private OrangeAclAOHTTP orangeAclAOHTTP;
    @Autowired
    private OrangeAclAOHSF orangeAclAOHSF;
    @Autowired
    private AppManager appManager;

    private static final String appKey = "531772";
    private static final String key = OrangeAclUtils.getReportKeyOfApp(appKey);
    private static final int bucId = 65153;

    @Test
    public void test() {

        boolean ret = orangeAclManager.checkAppReportPermission(appManager.selectAppByAppKey(appKey), bucId);

        System.out.println(">>" + ret);

    }

    @Test
    @Ignore
    public void testHSF() {
        boolean ret1 = orangeAclAOHSF.checkPermission(key, bucId);
        System.out.println("HSF>>" + ret1);

    }
}
