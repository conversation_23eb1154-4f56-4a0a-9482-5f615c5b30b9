package com.taobao.wireless.orange.console.manager.support;

import com.alibaba.fastjson.JSONObject;
import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.console.manager.NamespaceManager;
import com.taobao.wireless.orange.console.manager.support.bpms.OrangeBpmsManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class OrangeBpmsManagerDebug extends BaseManagerDebug {

    @Autowired
    private OrangeBpmsManager orangeBpmsManager;
    @Autowired
    private NamespaceManager namespaceManager;


    @Test
    public void testGetResult() throws Exception {
        // String bpmsId = "d24d48f0-3d5a-4d8e-bfcd-d185eb88e488";//拒绝
        String bpmsId = "a224c992-f4f6-4b66-89de-024070a6a385";//通过

        Map<String, String> map = orangeBpmsManager.getVariablesOfProcessInstance(bpmsId);
        System.out.print(">>" + JSONObject.toJSON(map));


    }

    @Override
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {

    }
}
