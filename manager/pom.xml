<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <parent>
        <groupId>com.taobao.wireless</groupId>
        <artifactId>orange-console</artifactId>
        <version>1.1.4</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>orange-console.manager</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId} ${project.version}</name>
    <properties>
        <spring.version>4.2.6.RELEASE</spring.version>
        <alipmc.version>1.5.3</alipmc.version>
        <fastjson.version>1.2.72_noneautotype</fastjson.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.taobao.accs</groupId>
            <artifactId>accs-service-sdk</artifactId>
        </dependency>
        <!--安全升级-->
        <dependency>
            <groupId>com.taobao.metaq.final</groupId>
            <artifactId>metaq-client</artifactId>
            <version>4.1.6.Final</version>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.eagleeye</groupId>
                    <artifactId>eagleeye-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.taobao.vipserver</groupId>
                    <artifactId>vipserver-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--BUC http client from-->
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>xxpt.gateway.shared.client</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.4</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.mtl</groupId>
            <artifactId>mtl-open-sdk-consumer</artifactId>
            <version>2.0</version>
        </dependency>
        <!--BUC http client end-->
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>mappcenter-client</artifactId>
            <version>1.3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>de.ruedigermoeller</groupId>
                    <artifactId>fst</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>orange-console.api</artifactId>
            <version>${orange-console.api}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>orange-console.core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.4.0.Final</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>18.0</version>
        </dependency>
        <dependency>
            <groupId>org.zeroturnaround</groupId>
            <artifactId>zt-zip</artifactId>
            <version>1.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>json-lib</artifactId>
                    <groupId>net.sf.json-lib</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.messenger</groupId>
            <artifactId>messenger-common</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <!--指定accs里的tair-->
        <dependency>
            <groupId>com.taobao.tair</groupId>
            <artifactId>tair-mc-client</artifactId>
            <version>2.2.22</version>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.vipserver</groupId>
                    <artifactId>vipserver-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.tair</groupId>
            <artifactId>tair-client</artifactId>
            <version>4.4.12</version>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.vipserver</groupId>
                    <artifactId>vipserver-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- mass http-->
        <dependency>
            <groupId>com.taobao.agoo</groupId>
            <artifactId>bean2http-client</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.10</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.csp</groupId>
            <artifactId>preplan.api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>switchcenter</artifactId>
                    <groupId>com.taobao.csp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.work.alipmc</groupId>
            <artifactId>alipmc-api</artifactId>
            <version>${alipmc.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>1.9.6</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.9.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>org.springframework.orm</artifactId>
            <version>3.2.4.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>org.springframework.aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>org.springframework.context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>org.springframework.beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>org.springframework.core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>org.springframework.core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.aopalliance</groupId>
                    <artifactId>com.springsource.org.aopalliance</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>org.springframework.jdbc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>org.springframework.transaction</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.accs</groupId>
            <artifactId>accs-batch-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.mtl.mupp</groupId>
            <artifactId>mupp-client</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.motu</groupId>
                    <artifactId>motu-acl-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.platform.app</groupId>
                    <artifactId>buc.keycenter.shared.client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.platform.shared</groupId>
                    <artifactId>webx3.extension.rpc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.citrus</groupId>
                    <artifactId>citrus-webx-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.platform.shared</groupId>
                    <artifactId>buc.api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jcl-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.china</groupId>
                    <artifactId>AliIMAgent</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.external</groupId>
                    <artifactId>java.mail</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.velocity</groupId>
                    <artifactId>velocity</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba-inc.mon</groupId>
                    <artifactId>noticentersdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.external</groupId>
                    <artifactId>java.mail</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.taobao.top</groupId>
                    <artifactId>top-api-sdk-dev</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.schedulerx</groupId>
            <artifactId>schedulerx-client</artifactId>
            <version>2.100.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.eagleeye</groupId>
                    <artifactId>eagleeye-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.taobao.common</groupId>
                    <artifactId>fulllinkstresstesting</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.schedulerx</groupId>
            <artifactId>schedulerx-worker</artifactId>
            <version>1.1.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>acl.api</artifactId>
            <version>2.3.15</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.common</groupId>
            <artifactId>fulllinkstresstesting</artifactId>
            <version>0.9.9.5</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>buc.sso.client</artifactId>
            <version>1.5.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>wmcc-client</artifactId>
            <version>1.0.1</version>
        </dependency>
        <!-- test -->
        <dependency>
            <groupId>com.taobao.hsf</groupId>
            <artifactId>LightApi</artifactId>
            <version>1.0.4-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.18</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.goc</groupId>
            <artifactId>changefree-client</artifactId>
            <version>2.0.13</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.mtl.datacenter</groupId>
            <artifactId>mtl-datacenter-client</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.5.1</version>
            <scope>test</scope>
        </dependency>
        <!-- 引入poi，解析workbook视图
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.16</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.14</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
            <version>2.6.10</version>
        </dependency>-->
        <dependency>
            <groupId>com.taobao.tair</groupId>
            <artifactId>tair-mc-client</artifactId>
            <version>4.4.12</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.emas</groupId>
            <artifactId>emas-ha-open-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.mtl</groupId>
                    <artifactId>mtl-open-protocol</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>tablestore</artifactId>
            <version>5.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.ihr</groupId>
            <artifactId>amdplatform-service-api</artifactId>
            <version>2.0.88</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.diamond</groupId>
            <artifactId>diamond-client</artifactId>
            <version>3.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.4.63</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>ahas-switch-client</artifactId>
            <version>1.1.20-PANDORA</version>
        </dependency>
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>dingtalk-openapi-sdk</artifactId>
            <version>1479188381469-20211207</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log</artifactId>
            <version>0.6.75</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>tiga-release-expression-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>tiga-release-console-api</artifactId>
        </dependency>
    </dependencies>
</project>
