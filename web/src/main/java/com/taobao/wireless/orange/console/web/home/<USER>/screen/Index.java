package com.taobao.wireless.orange.console.web.home.module.screen;

import com.alibaba.citrus.service.requestcontext.parser.ParameterParser;
import com.alibaba.citrus.turbine.Context;
import com.alibaba.citrus.turbine.Navigator;
import com.taobao.wireless.orange.console.manager.config.switcher.SwitchConfig;
import com.taobao.wireless.orange.console.manager.model.User;
import com.taobao.wireless.orange.console.manager.support.AnnouncementManager;
import com.taobao.wireless.orange.console.manager.support.diamond.DiamondCommonDataManager;
import com.taobao.wireless.orange.console.web.home.module.AbstractScreenBase;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

public class Index extends AbstractScreenBase {


    @Autowired
    protected DiamondCommonDataManager diamondCommonDataManager;

    @Autowired
    private AnnouncementManager announcementManager;

    @Override
    protected void call(ParameterParser requestParams, Context context, Navigator nav) throws Exception {
        context.put("assertCssUrl", diamondCommonDataManager.getAssertCssUrl());
        context.put("assertJsUrl", diamondCommonDataManager.getAssertJsUrl());
        User user = getUser();
        context.put("empId", user.getEmpId());
        context.put("userNickName", this.getOperator());
        context.put("emailPrefix", user.getEmailPrefix());
        context.put("isAdmin", this.isOrangeAdminUser());
        context.put("announcement",announcementManager.getAnnouncement());
        context.put("newPlatformEnabled", true);
    }
}
